using System;
using Isto.Core.Game;
using Isto.Core.Inputs;
using Isto.GTW;
using Isto.GTW.Inputs;
using Isto.GTW.Player;
using UnityEngine;
using Zenject;

namespace UI.ReadyScreen
{
    public class ReadyScreen : MonoBehaviour
    {
        // UNITY HOOKUPS

        [SerializeField] private CanvasGroup _canvasGroup = null;
        [SerializeField] private GameObject _keyboardMessage = null;
        [SerializeField] private GameObject _controllerMessage = null;
        [Tooltip("Delay before being able to hide the ready screen")]
        [SerializeField] private float _inputCheckDelay = 0.5f;
        
        
        // OTHER FIELDS

        private bool _isVisible = false;
        private float _showDuration = -1f;
        
        
        // PROPERTIES
        
        public static bool ShowReadyScreen { get; set; } = false;
        
        
        // INJECTION
        
        private PlayerController _playerController;
        private IControls _controls;

        [Inject]
        public void Inject(PlayerController playerController, IControls controls)
        {
            _playerController = playerController;
            _controls = controls;
        }
        
        // UNITY LIFECYCLE

        private void Awake()
        {
            _canvasGroup.gameObject.SetActive(false);
        }

        private void Update()
        {
            if (ShowReadyScreen && !_isVisible)
            {
                Show();
            }

            if (_isVisible && _playerController.IsMoveState)
            {
                if (!Mathf.Approximately(Time.timeScale, 0f))
                {
                    Time.timeScale = 0f;
                }
                
                UpdateControllerKeyboard();
                
                _showDuration += Time.unscaledDeltaTime;

                if (_showDuration > _inputCheckDelay && (_controls.GetAnyButton() || _controls.GetStartGameAction() || _controls.GetButton(GTWUserActions.STARTGAME)))
                {
                    ShowReadyScreen = false;
                    Hide();
                }
            }
        }
        
        
        // OTHER METHODS

        public void Show()
        {
            _isVisible = true;
            _canvasGroup.gameObject.SetActive(true);
            // Time.timeScale = 0f;
            _showDuration = 0f;
        }
        
        public void Hide()
        {
            _isVisible = false;
            _canvasGroup.gameObject.SetActive(false);
            Time.timeScale = 1f;
        }

        private void UpdateControllerKeyboard()
        {
            _keyboardMessage.SetActive(!_controls.UsingJoystick());
            _controllerMessage.SetActive(_controls.UsingJoystick());
        }
    }
}