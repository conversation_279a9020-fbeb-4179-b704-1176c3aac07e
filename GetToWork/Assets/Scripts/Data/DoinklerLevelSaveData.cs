using Isto.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Serialization;
using Object = System.Object;

namespace Isto.GTW.Data
{
    [Serializable, XmlRoot("DoinklerLevelData")]
    public class DoinklerLevelSaveData
    {
        public class DoinklerLevelTime
        {
            public int levelID;
            public float bestTime;
            public float totalTime;

            public override bool Equals(object obj)
            {
                DoinklerLevelTime other = obj as DoinklerLevelTime;

                if (other == null)
                    return false;
                if (this.levelID != other.levelID)
                    return false;

                return this.bestTime.Approx(other.bestTime);
            }

            public override int GetHashCode()
            {
                return (levelID, bestTime, totalTime).GetHashCode();
            }
        }

        public class EntryBool
        {
            public string Key;
            public bool Value;
            public EntryBool()
            {
            }

            public EntryBool(string key, bool value)
            {
                Key = key;
                Value = value;
            }

            public override bool Equals(Object obj)
            {
                // Check for null values and compare run-time types.
                if (obj == null || GetType() != obj.GetType())
                    return false;

                EntryBool p = (EntryBool)obj;
                return (Key == p.Key) && (Value == p.Value);
            }

            public override int GetHashCode()
            {
                return Key.GetHashCode() ^ Value.GetHashCode();
            }
        }

        public class EntryInt
        {
            public string Key;
            public int Value;
            public EntryInt()
            {
            }

            public EntryInt(string key, int value)
            {
                Key = key;
                Value = value;
            }

            public override bool Equals(Object obj)
            {
                // Check for null values and compare run-time types.
                if (obj == null || GetType() != obj.GetType())
                    return false;

                EntryInt p = (EntryInt)obj;
                return (Key == p.Key) && (Value == p.Value);
            }

            public override int GetHashCode()
            {
                return Key.GetHashCode() ^ Value.GetHashCode();
            }
        }

        public class EntryFloat
        {
            public string Key;
            public float Value;
            public EntryFloat()
            {
            }

            public EntryFloat(string key, float value)
            {
                Key = key;
                Value = value;
            }

            public override bool Equals(Object obj)
            {
                // Check for null values and compare run-time types.
                if (obj == null || GetType() != obj.GetType())
                    return false;

                EntryFloat p = (EntryFloat)obj;
                return (Key == p.Key) && (Value == p.Value);
            }

            public override int GetHashCode()
            {
                return Key.GetHashCode() ^ Value.GetHashCode();
            }
        }

        public List<EntryBool> boolVariables = new List<EntryBool>();
        public List<EntryInt> intVariables = new List<EntryInt>();
        public List<EntryFloat> floatVariables = new List<EntryFloat>();
        public List<DoinklerLevelTime> gameTimePerLevel;

        public bool ContentEquals(DoinklerLevelSaveData other)
        {
            if (other == null)
                return false;

            if (!this.boolVariables.All(other.boolVariables.Contains))
            {
                return false;
            }
            if (!this.intVariables.All(other.intVariables.Contains))
            {
                return false;
            }
            if (!this.floatVariables.All(other.floatVariables.Contains))
            {
                return false;
            }

            // GTWExtensions.DictionaryComparer<string, bool> boolComparer = new GTWExtensions.DictionaryComparer<string, bool>();
            // if (boolComparer.Equals(this.boolVariables, other.boolVariables))
            //     return false;
            //
            // GTWExtensions.DictionaryComparer<string, int> intComparer = new GTWExtensions.DictionaryComparer<string, int>();
            // if (intComparer.Equals(this.intVariables, other.intVariables))
            //     return false;
            //
            // GTWExtensions.DictionaryComparer<string, float> floatComparer = new GTWExtensions.DictionaryComparer<string, float>();
            // if (floatComparer.Equals(this.floatVariables, other.floatVariables))
            //     return false;


            if (!this.gameTimePerLevel.SequenceEqualOrNull(other.gameTimePerLevel))
            {
                return false;
            }

            return true;
        }

        public void AddLevelTime(int level, float bestTime, float totalTime)
        {
            if (gameTimePerLevel == null)
                gameTimePerLevel = new List<DoinklerLevelTime>();

            gameTimePerLevel.Add(new DoinklerLevelTime() { levelID = level, bestTime = bestTime, totalTime = totalTime });
        }

        public Dictionary<int, Tuple<float, float>> GetLevelTimes()
        {
            Dictionary<int, Tuple<float, float>> dict = new Dictionary<int, Tuple<float, float>>();
            foreach (DoinklerLevelTime pair in gameTimePerLevel)
            {
                dict.Add(pair.levelID, new Tuple<float, float>(pair.bestTime, pair.totalTime));
            }
            return dict;
        }
    }
}