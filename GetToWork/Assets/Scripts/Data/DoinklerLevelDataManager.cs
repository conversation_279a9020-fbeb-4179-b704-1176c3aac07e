using Isto.Core.Data;
using Isto.Core.Game;
using Isto.GTW.Managers;
using System.Collections.Generic;
using System.IO;
using System.Xml.Serialization;
using Zenject;

namespace Isto.GTW.Data
{
    /// <summary>
    /// Serializes the dictionaries of LevelVariables to save into the save file.
    ///
    /// Converts the dictionary to a list (since you can't serialize a dictionary)
    /// </summary>
    public class DoinklerLevelDataManager : DataManager, IDataLoadCompleteHandler
    {
        private const string FILE_NAME = "best_times";
        private const string FILE_PREFIX = "/" + FILE_NAME + "_";

        public override string FilePrefix => FILE_PREFIX;
        public override string BlobName => FILE_NAME;


        // INJECTION

        private DoinklerLevelVariables _doinklerLevelVariables;
        private GTWProgressProvider _gameProgress;

        [Inject]
        public void Inject(DoinklerLevelVariables doinklerLevelVariables, IGameProgressProvider gameProgress)
        {
            _doinklerLevelVariables = doinklerLevelVariables;
            _gameProgress = gameProgress as GTWProgressProvider;
        }


        // OTHER METHODS

        public override bool Validate(in TextReader reader, in object previousDataObject)
        {
            bool valid = false;

            DoinklerLevelSaveData previousData = previousDataObject as DoinklerLevelSaveData;
            DoinklerLevelSaveData loadedData = LoadXMLFile<DoinklerLevelSaveData>(reader);

            if (loadedData != null)
            {
                valid = previousData.ContentEquals(loadedData);
            }

            return valid;
        }

        public override bool Load(in TextReader reader)
        {
            bool success = false;
            DoinklerLevelSaveData doinklerLevelSaveData = LoadXMLFile<DoinklerLevelSaveData>(reader);

            if (doinklerLevelSaveData != null)
            {
                _gameProgress.LoadLevelTimes(doinklerLevelSaveData.GetLevelTimes());

                _doinklerLevelVariables.boolVariables = new Dictionary<string, bool>();
                foreach (DoinklerLevelSaveData.EntryBool entry in doinklerLevelSaveData.boolVariables)
                {
                    _doinklerLevelVariables.boolVariables.Add(entry.Key, (bool)entry.Value);
                }

                _doinklerLevelVariables.intVariables = new Dictionary<string, int>();
                foreach (DoinklerLevelSaveData.EntryInt entry in doinklerLevelSaveData.intVariables)
                {
                    _doinklerLevelVariables.intVariables.Add(entry.Key, (int)entry.Value);
                }

                _doinklerLevelVariables.floatVariables = new Dictionary<string, float>();
                foreach (DoinklerLevelSaveData.EntryFloat entry in doinklerLevelSaveData.floatVariables)
                {
                    _doinklerLevelVariables.floatVariables.Add(entry.Key, (float)entry.Value);
                }

                _doinklerLevelVariables.OutputVariables();

                success = true;
            }

            return success;
        }

        public override void Save(out object saveData)
        {
            List<DoinklerLevelSaveData.EntryBool> boolVars = new List<DoinklerLevelSaveData.EntryBool>();
            foreach (string key in _doinklerLevelVariables.boolVariables.Keys)
            {
                boolVars.Add(new DoinklerLevelSaveData.EntryBool(key, _doinklerLevelVariables.boolVariables[key]));
            }
            List<DoinklerLevelSaveData.EntryInt> intVars = new List<DoinklerLevelSaveData.EntryInt>();
            foreach (string key in _doinklerLevelVariables.intVariables.Keys)
            {
                intVars.Add(new DoinklerLevelSaveData.EntryInt(key, _doinklerLevelVariables.intVariables[key]));
            }
            List<DoinklerLevelSaveData.EntryFloat> floatVars = new List<DoinklerLevelSaveData.EntryFloat>();
            foreach (string key in _doinklerLevelVariables.floatVariables.Keys)
            {
                floatVars.Add(new DoinklerLevelSaveData.EntryFloat(key, _doinklerLevelVariables.floatVariables[key]));
            }

            DoinklerLevelSaveData doinklerLevelSaveData = new DoinklerLevelSaveData()
            {
                boolVariables = boolVars,
                intVariables = intVars,
                floatVariables = floatVars
            };

            // for (int i = 0; i < 10; i++)
            // {
            //     float time = _gameProgress.GetGameSecondsElapsedInLevel(i);
            //     doinklerLevelSaveData.AddLevelTime(i, time);
            // }

            saveData = doinklerLevelSaveData;
        }

        public override object GetSampleSaveData()
        {
            DoinklerLevelSaveData DoinklerLevelSaveData = new DoinklerLevelSaveData();
            return DoinklerLevelSaveData;
        }

        public override System.Type[] GetSaveExtraTypes()
        {
            return null;
        }

        public override void UpgradeSave(string targetVersion, GameStateSaveData metaData, TextReader reader, out XmlSerializer serializer,
            out object saveData)
        {
            serializer = null;
            saveData = null;
        }

        public void OnDataLoadComplete()
        {

        }
    }
}