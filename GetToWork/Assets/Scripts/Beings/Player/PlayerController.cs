// Copyright Isto Inc.
using System;
using System.Collections.Generic;
using Isto.GTW.Inputs;
using Isto.Core;
using Isto.Core.Beings;
using Isto.Core.Inputs;
using Isto.Core.StateMachine;
using Isto.GTW.Camera;
using Isto.GTW.DebugTools;
using Isto.GTW.LevelFeatures;
using Isto.GTW.Managers;
using Isto.GTW.Player.Audio;
using Isto.GTW.Player.States;
using Isto.GTW.SectionLoading;
using Isto.GTW.Tools;
using UnityEngine;
using Zenject;
using System.Text;
using Cinemachine;
using RootMotion.FinalIK;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Isto.GTW.Player
{
    [DefaultExecutionOrder(100)]
    /// <summary>
    /// <newline>
    /// A giant class that takes on (probably too many) several tasks
    /// Handles state machine, player phsyics, and player abilities (grab ability, no clip, but not ragdoll)
    /// It should be cleaner, but this project is quick and dirty, so we've left it as is.
    /// </summary>
    public class PlayerController : CorePlayerController
    {
        // UNITY HOOKUP
        [SerializeField] private PlayerModelController _playerModelController = null;

        [Space]
        [SerializeField] private Transform _playerCameraTransform = null;
        [SerializeField] private Rigidbody _rigidbody = null;
        [SerializeField] private Collider _mainHitBox = null;
        [SerializeField] private SphereCollider _mainBallCollider = null;

        // When switching between ragdoll and regular mode, we disable the player physics (the ball) and enable the ragdoll physics.
        // Because of this, we need two event triggers for the different states.

        // For non-ragdoll mode only - attached to the ball physics.
        [SerializeField] protected SphereCollider _playerEventTrigger = null;
        // For ragdoll mode only - attached to the pelvis.
        [SerializeField] protected SphereCollider _bodyEventTrigger = null;

        [Space]
        [Header("Grab Ability")]
        [Tooltip("The duration (s) after you touch the ground that will still allow you to activate grab-mode")]
        // You can only grab when the player is touching a surface, and it feels better to
        // give users a bit of a grace period to still grab an edge.
        [SerializeField] private float _grabGracePeriod = 0.05f;

        [Tooltip("The amount of force required to \"break\" the grab.\n" +
            "Usually occurs when you get pushed by another moving object.\n\n" +
            "Force is a Unity unit (distance per second)\n" +
            "Value is chosen by feel - a small value, but not small enough that it'll break the grab by itself.")]
        // One example would be the laser-triggered pusher in the final tower level.
        [SerializeField] private float _grabBreakForce = 0.1f;

        [Tooltip("The amount of force applied to the character when swinging in ledge-grab mode.\n" +
                 "Applied to the pelvis of the character. The anchor is at the players' hands.\n\n" +
                 "Force is a Unity unit (m/s).\n" +
                 "\nValue is chosen by feel.")]
        [SerializeField] private float _ledgeGrabForceControl = 500f;

        [Tooltip("A force applied to the character when letting go of ledge-grab mode.\n" +
                 "Value is chosen by feel.")]
        // Used to help players recover from an edge-grab.
        // Changes based on the players rotation to make it more exciting to potentially recover
        // while also adding skill to the recovery.
        // For example, if the player is hanging normally (body is straight down), it doesn't apply any force,
        // and the player will for sure fall.
        // If the player is horizontal it gives you the full force in the up direction, giving the player a little "boost" upwards.
        [SerializeField] private float _ledgeGrabFlingVerticalForce = 3f;

        [Space]
        [SerializeField] protected Transform _debugBallTransform = null;

        [Space]
        [Header("States")]
        public GTWPlayerMoveState _moveState = null;
        public GTWPlayerWaitForSectionLoading _waitForSectionLoading = null;
        public GTWPlayerNoClipState _noClipState = null;
        public GTWPlayerRagdollState _ragdollState = null;
        public GTWPlayerCutsceneState _cutsceneState = null;
        public GTWPlayerFreezeControls _freezeControlsState = null;

        [Space]
        [Header("Abilities")]
        public bool grabAbilityAvailable = true;
        public bool slowMotionAbilityAvailable = true;
        public bool noClipAbilityAvailable = true;
        public bool ragDollAbilityAvailable = false; // start the game without it
        public bool restartAbilityAvailable = false;
        public bool restartStartingCheckpointAbilityAvailable = false;
        public bool noClipHighSpeedMode = false;
        public float noClipHighSpeedMultiplier = 3f;

        [Space]
        [SerializeField] private TorquePhysics _torquePhysics = new TorquePhysics();

        [Space]
        public bool showDebug = true;


        // PROPERTIES

        public static PlayerController CurrentPlayer
        {
            get;
            private set;
        }

        public TorquePhysics TorquePhysics => _torquePhysics;

        public CheckpointManager CheckpointManager { get; set; }

        public PlayerModelController PlayerModelController => _playerModelController;
        public PlayerIkController PlayerIkController => PlayerModelController.PlayerIkController;
        public IControls InputControls => _controls;
        public PlayerCollisionHandler PlayerCollisionHandler => _playerCollisionHandler;
        public PlayerAudio PlayerAudio => _playerAudio;
        public PlayerWearableController PlayerWearableController => _playerWearableController;
        public PlayerVirtualCameraController PlayerVirtualCameraController => _playerVirtualCameraController;
        public SphereCollider PlayerEventTrigger => _playerEventTrigger;
        public SphereCollider BodyEventTrigger => _bodyEventTrigger;

        public TimeManager TimeManager => _timeManager;

        protected override Collider MainHitBox => _mainHitBox;
        public SphereCollider MainBallCollider => _mainBallCollider;
        public Rigidbody Rigidbody => _rigidbody;
        public float BallRadius => TorquePhysics.BallRadius;

        public bool IsMoveState => _currentState?.GetType() == typeof(GTWPlayerMoveState);
        public bool IsFreezeControlsState => _currentState?.GetType() == typeof(GTWPlayerFreezeControls);
        public bool IsRagdollState => _currentState?.GetType() == typeof(GTWPlayerRagdollState);

        public Transform PlayerTransform => _rigidbody.transform;
        public Vector3 PlayerPosition => PlayerTransform.position;
        public Transform PlayerCameraTransform => _playerCameraTransform;
        public Vector3 Velocity => _velocity;
        public Vector3 BallSurfaceVelocity => _ballSurfaceVelocity;
        public float Speed => _speed;

        public Vector3 BodyVelocity => _bodyVelocity;
        public float BodySpeed => _bodySpeed;
        public float BodySpeedKPH => _bodySpeedKPH;

        public Vector3 VelocityAcceleration => _velocityAcceleration;
        public float VelocityAccelerationSpeed => _velocityAccelerationSpeed;
        public Vector3 VelocityAccelerationRaw => _velocityAccelerationRaw;
        public float VelocityAccelerationSpeedRaw => _velocityAccelerationSpeedRaw;
        public Vector3 Direction => _direction;
        public Vector3 DirectionUp => _directionUp;
        public Vector3 DirectionFlat => _directionFlat;
        public Vector3 DirectionSmooth => _directionSmooth;
        public Vector3 DirectionUpSmooth => _directionUpSmooth;
        public Vector3 DirectionFlatSmooth => _directionFlatSmooth;
        public Vector3 GravityDirection => TorquePhysics.GravityDirection;
        public Vector3 Gravity => TorquePhysics.Gravity;

        public Vector3 AngularVelocityDirection => _angularVelocityDirection;
        public Vector3 AngularVelocityUp => _angularVelocityUp;
        public Vector3 AngularVelocityDirectionSmooth => _angularVelocityDirectionSmooth;


        public PlayerPhysicsOverride PlayerPhysicsOverride => _playerPhysicsOverride;


        public Vector3 RawInput => _rawInput;
        public Vector3 MovementInput => _movementInput;
        public Vector3 CharacterSpaceInput => _characterSpaceInput;

        public float CurrentTorque => Rigidbody.angularVelocity.magnitude;


        // When you're in ragdoll mode, use the speed of the rigidbody on the pelvis
        public float CurrentSpeed => IsRagdollState ? _bodySpeed : _speed;
        public float CurrentSpeedKPH => IsRagdollState ? _bodySpeedKPH : _speedKPH;
        public float CurrentBallSurfaceSpeed => _ballSurfaceSpeed;

        // 3600f/1000f is the standard conversion of meters/sec -> km/hr
        public float CurrentBallSurfaceSpeedKPH => _ballSurfaceSpeed * 3.6f;

        public bool IsGrabLinkActive => _isGrabLinkActive;
        public bool IsGrabRagDoll => _grabRagdoll;
        public bool GrabbingStickySurface => _grabbingStickySurface;
        public Collider GrabLinkCollider => _grabLinkCollider;
        public Vector3 GrabLinkNormal => _grabLinkNormal;
        public Transform GrabLinkParent => _grabLinkParent;
        public Rigidbody GrabLinkParentRigidbody => _grabLinkParentRigidbody;
        public Vector3 GrabTargetPosition => _grabLinkParent.TransformPoint(_grabLocalLocation.Position);
        public Quaternion GrabTargetRotation => _grabLinkParent.rotation * _grabLocalLocation.Rotation;
        public bool HighFrictionMode => _highFrictionModeTime > 0f;

        public List<string> SceneNameCollidersToIgnore => _sceneNameCollidersToIgnore;



        public bool ForceHoldGrab
        {
            get
            {
                return _forceHoldGrab;
            }
            set
            {
                _forceHoldGrab = value;
            }
        }

        public override State MovementState => _moveState;

        // Even when the player is stopped, it can sometimes move a tiny bit that isn't visible to the player
        // Using a really small number avoids false positives.
        public override bool IsMoving => _speed > 0.0001f;
        public bool IsBodyMoving => _bodySpeed > 0.0001f;

        public float TimeSinceLastMoving => _timeSinceLastMoving;

        public Transform DebugBallTransform => _debugBallTransform;


        // OTHER FIELDS

        protected PlayerVirtualCameraController _playerVirtualCameraController;
        protected PlayerCollisionHandler _playerCollisionHandler = null;
        protected PlayerAudio _playerAudio = null;
        protected TimeManager _timeManager = null;
        protected PlayerWearableController _playerWearableController = null;

        /// <summary>
        /// Note: there are two velocities - Rigidbody.velocity and _velocity
        /// The unity physics system runs independantly of the rendering frame rate
        /// (i.e. Rigidbody.velocity is updated in the FixedUpdate).
        ///
        /// However, for some use cases, we want to use Rigidboy.velocity in the Update() loop
        /// but doing so will cause stuttering (since the rigidbodies position may be out of sync
        /// with the position being rendered). To avoid this issue, we calculate our own velocity based on
        /// the transform position of the ball (i.e playerphysics) previous frame and current frame in the Update()
        /// </summary>
        protected Vector3 _velocity = Vector3.zero;

        protected float _speed = 0f;
        protected float _speedKPH = 0f;

        protected float _timeSinceLastMoving = 0f;

        /// <summary>
        /// The Rigidbody's velocity to calculate acceleration causes stuttering issues.
        /// Thus, we use our _velocity to calculate acceleration.
        /// See _velocity for more details.
        /// </summary>
        protected Vector3 _velocityAcceleration = Vector3.zero;
        protected float _velocityAccelerationSpeed = 0f;
        protected Vector3 _velocityAccelerationRaw = Vector3.zero;
        protected float _velocityAccelerationSpeedRaw = 0f;
        protected Vector3 _direction = Vector3.forward;//Direction in Update() Time
        protected Vector3 _directionUp = Vector3.up;
        protected Vector3 _directionFlat = Vector3.forward;

        protected Vector3 _oldDirectionSmooth = Vector3.forward;
        protected Vector3 _directionSmooth = Vector3.forward;
        protected Vector3 _directionUpSmooth = Vector3.forward;
        protected Vector3 _directionFlatSmooth = Vector3.forward;

        protected Vector3 _angularVelocityDirection = Vector3.forward;
        protected Vector3 _angularVelocityUp = Vector3.up;

        protected Vector3 _oldAngularVelocityDirectionSmooth = Vector3.forward;
        protected Vector3 _angularVelocityDirectionSmooth = Vector3.forward;

        /// <summary>
        /// The speed of the ball (in KM/H) at its extremities (i.e the surface).
        /// Used to detect slipping (to play the wheel screeching sound).
        /// If this speed differs from the current speed, it means means the ball is slipping/drifting.
        /// </summary>
        protected Vector3 _ballSurfaceVelocity = Vector3.zero;
        protected float _ballSurfaceSpeed = 0f;

        //Stores the velocity of the player model when in both movement and ragdoll mode
        protected Vector3 _bodyVelocity = Vector3.zero;
        //Stores the speed of the player model when in both movement and ragdoll mode
        protected float _bodySpeed = 0f;
        protected float _bodySpeedKPH = 0f;


        protected float _noClipVerticalInput = 0f;
        protected Vector3 _noClipVelocity = Vector3.zero;


        protected Vector3 _rawInput = Vector3.zero;
        protected Vector3 _movementInput = Vector3.zero;
        protected Vector3 _characterSpaceInput = Vector3.zero;

        private PlayerPhysicsOverride _playerPhysicsOverride = null;

        private Vector3 _lastPosition = Vector3.zero;
        private Quaternion _lastRotation = Quaternion.identity;
        private float _lastSpeed = 0f;
        private Vector3 _lastVelocity = Vector3.zero;
        private float _lastSpeedRaw = 0f;
        private Vector3 _lastVelocityRaw = Vector3.zero;

        private bool _isGrabLinkActive = false;
        private bool _forceHoldGrab = false;
        private bool _grabbingStickySurface = false;

        // The current scene name colliders we wish to ignore
        private List<string> _sceneNameCollidersToIgnore = new List<string>();

        // A value that feels good.
        private const float GRAB_COOLDOWN = 0.25f;
        private float _grabCooldownTimeLeft = GRAB_COOLDOWN;
        private bool _grabRagdoll = false;
        private Transform _grabLinkParent = null;
        private Collider _grabLinkCollider = null;
        private Rigidbody _grabLinkParentRigidbody = null;
        private Vector3 _grabLinkNormal = Vector3.zero;
        private GTWExtensions.Location _grabLocalLocation;
        private Vector3 _lastGrabTargetPosition;
        private Queue<Vector3> _lastGrabVelocities = new Queue<Vector3>();
        private Vector3 _lastGrabVelocity = Vector3.zero;
        private Vector3 _lastGrabRigidbodyVelocity = Vector3.zero;

        private float _highFrictionModeTime = -1f;
        private int _slipperyLayer;

        private bool _hasPlayerInputted = false;

        // EVENTS

        public Action OnResetVelocity;
        public Action OnPlayerTeleport;
        public Action OnPlayerRespawn;
        public Action OnActivateGrab;
        public Action OnDeactivateGrab;


        // INJECTION

        [Inject]
        public void Inject(TimeManager timeManager)
        {
            _timeManager = timeManager;
        }


        // LIFECYCLE EVENTS

        private void Reset()
        {
            _rigidbody = GetComponentInChildren<Rigidbody>();
        }

        protected override void Awake()
        {
            CurrentPlayer = this;
            TorquePhysics.PlayerController = this;

            base.Awake();

            _playerCollisionHandler = GetComponentInChildren<PlayerCollisionHandler>();
            _playerVirtualCameraController = GetComponentInChildren<PlayerVirtualCameraController>();
            _playerPhysicsOverride = GetComponent<PlayerPhysicsOverride>();
            _playerAudio = GetComponentInChildren<PlayerAudio>();
            _playerWearableController = GetComponentInChildren<PlayerWearableController>();

            _slipperyLayer = LayerMask.NameToLayer("Slippery");

            ResetVelocityToZero();
            DeactivateGrabLink();
            SetPhysicsActive(true);

            _torquePhysics.Initialize();

            Events.Subscribe(GTWEvents.GIVE_UP_ALLOWED, Events_OnGiveUpAllowed);
            Events.Subscribe(GTWEvents.DOINKLER_STAGE_START, Events_OnDoinklerStageStart);
        }

        private void OnDestroy()
        {
            Events.UnSubscribe(GTWEvents.GIVE_UP_ALLOWED, Events_OnGiveUpAllowed);
            Events.UnSubscribe(GTWEvents.DOINKLER_STAGE_START, Events_OnDoinklerStageStart);
        }

        protected override void OnEnable()
        {
             base.OnEnable();
        }

        protected override void OnDisable()
        {
            // base.OnDisable();
        }

        /// <summary>
        /// Always use FixedUpdate() for physics.
        /// In unity, the Physics framerate is independant of the rendering framerate.
        ///
        /// If you apply Physics in the update, theres a risk it'll get applied twice
        /// </summary>
        protected virtual void FixedUpdate()
        {
            if (IsMoveState || IsFreezeControlsState)
            {
                UpdateGrabLinkFixedUpdate();
            }

            _highFrictionModeTime -= FixedTimeSync.FixedDeltaTime;

            _torquePhysics.PhysicsUpdate();
        }

        protected override void Update()
        {
            base.Update();

            Vector3 previousMovementInput = _movementInput;

            _movementInput = GetCameraSpaceMovementInput();
            _characterSpaceInput = GetCharacterSpaceMovementInput();

            if (IsMoveState || IsFreezeControlsState)
            {
                UpdateGrabLink();
            }

            if (!IsGrabLinkActive)
            {
                _grabCooldownTimeLeft -= Time.deltaTime;
            }

            if (showDebug)
            {
                // Debug.Log("_movementInput:" + _movementInput);
                DebugExtensions.DrawArrow(PlayerTransform.position, 0.1f, _direction * 1.5f, Color.red);
                DebugExtensions.DrawArrow(PlayerTransform.position, PlayerTransform.position + _angularVelocityDirection * 2f, Color.green);
                DebugExtensions.DrawArrow(PlayerTransform.position, 0.1f, _movementInput, new Color(1f, 0.3f, 0f, 1f));
            }
            /*
            bool errorDetected = false;
            errorDetected |= Input.GetKeyDown(KeyCode.P);
            if (errorDetected)
            {
                PrintCharacterInfo();
            }*/
        }


        // EVENT HANDLING

        private void Events_OnGiveUpAllowed()
        {
            // now the player has it forever for this playthrough because this will be saved
            ragDollAbilityAvailable = true;
        }

        private void Events_OnDoinklerStageStart()
        {
            _hasPlayerInputted = false;
        }


        // ACCESSORS

        public void SetIgnoreLevelDataColliders(List<string> sceneNameCollidersToIgnore)
        {
            _sceneNameCollidersToIgnore = sceneNameCollidersToIgnore;
        }


        // OTHER METHODS

        public override void ChangeToMoveState()
        {
            ChangeState(_moveState);
        }

        public override void ChangeToCustomState(State state)
        {
            ChangeState(state);
        }

        public void ChangeToRagdollState()
        {
            ChangeState(_ragdollState);
        }

        public void ChangeToWaitForSectionLoadingState()
        {
#if UNITY_EDITOR
            if (EditorPrefs.GetBool(SectionLoader.LOADING_DISABLED_EDITOR_PREF_KEY, false))
            {
                return;
            }
#endif

            ChangeState(_waitForSectionLoading);
        }
        
        public void ChangeToWaitForSectionLoadingState(Vector3 velocity, Vector3 angularVelocity)
        {
#if UNITY_EDITOR
            if (EditorPrefs.GetBool(SectionLoader.LOADING_DISABLED_EDITOR_PREF_KEY, false))
            {
                return;
            }
#endif

            ChangeState(_waitForSectionLoading);
            
            _waitForSectionLoading.keepVelocity = true;
            _waitForSectionLoading.velocity = velocity;
            _waitForSectionLoading.angularVelocity = angularVelocity;
        }

        public void ChangeToFreezeControlsState()
        {
            ChangeState(_freezeControlsState);
        }

        public override void Respawn()
        {
            OnPlayerRespawn?.Invoke();
        }

        public void RespawnAtCheckpoint(CheckpointData.LevelCheckpointData checkpoint)
        {
            Debug.Log("Respawning Player at checkpoint:" + checkpoint.checkpointName + " in scene:" + checkpoint.sceneName);
            Respawn();
            TeleportCharacter(checkpoint.position, checkpoint.forward);

            if (checkpoint.spawnOnGround)
            {
                _waitForSectionLoading.teleportToGroundOnExit = true;
            }
            ResetVelocityToZero();
            PlayerVirtualCameraController.UpdateCamera(true);
            ChangeToWaitForSectionLoadingState();
        }

        public override Vector3 GetPlayerPosition()
        {
            return PlayerTransform.position;
        }

        public void TeleportCharacter(Vector3 position)
        {
            Rigidbody.position = position;
            PlayerTransform.position = position;

            OnPlayerTeleport?.Invoke();
        }

        public void TeleportCharacter(Vector3 position, Vector3 forward)
        {
            Rigidbody.position = position;
            Rigidbody.rotation = Quaternion.LookRotation(forward, Vector3.up);
            PlayerTransform.position = position;
            PlayerTransform.rotation = Quaternion.LookRotation(forward, Vector3.up);

            OnPlayerTeleport?.Invoke();
        }

        public void TeleportCharacter(Vector3 position, Quaternion orientation)
        {
            Rigidbody.position = position;
            Rigidbody.rotation = orientation;
            PlayerTransform.position = position;
            PlayerTransform.rotation = orientation;

            OnPlayerTeleport?.Invoke();
        }

        public bool TeleportToGround(float maxDistance = 5f, float verticalStartHeightOffset = 0f)
        {
            RaycastHit hit;
            if (Physics.Raycast(PlayerPosition + new Vector3(0f, verticalStartHeightOffset, 0f), GravityDirection, out hit, maxDistance,
                _playerCollisionHandler.GroundLayerMask))
            {
                TeleportCharacter(hit.point + -GravityDirection * BallRadius);
                // PlayerVirtualCameraController.UpdateCamera();
                return true;
            }

            return false;
        }

        public void ResetVelocityToZero()
        {
            Rigidbody.linearVelocity = Vector3.zero;
            Rigidbody.angularVelocity = Vector3.zero;

            _direction = PlayerTransform.forward;
            _directionUp = PlayerTransform.up;
            _directionFlat = _direction;
            _directionFlat.y = 0f;
            _directionFlat.Normalize();
            _lastPosition = PlayerTransform.position;
            _lastRotation = PlayerTransform.rotation;

            _velocity = Vector3.zero;//Velocity in Update() Time
            _speed = 0f;
            _speedKPH = 0f;
            _bodySpeedKPH = 0f;
            _velocityAcceleration = Vector3.zero;
            _velocityAccelerationSpeed = 0f;

            _angularVelocityDirection = _direction;
            _angularVelocityUp = _directionUp;

            _lastSpeed = 0f;
            _lastVelocity = Vector3.zero;

            OnResetVelocity?.Invoke();
        }

        public void SetVelocity(Vector3 velocity, Vector3 angularVelocity)
        {
            Rigidbody.linearVelocity = velocity;
            Rigidbody.angularVelocity = angularVelocity;

            _velocity = velocity;
            _speed = velocity.magnitude;
            _speedKPH = _speed * 3.6f;
            _velocityAcceleration = Vector3.zero;
            _velocityAccelerationSpeed = 0f;

            _lastSpeed = _speed;
            _lastVelocity = velocity;
        }

        /// <summary>
        /// Enable/disables the balls physics (which is used for entering/exiting ragdoll mode)
        /// </summary>
        public void SetPhysicsActive(bool isActive)
        {
            Rigidbody.gameObject.SetActive(isActive);

            _playerEventTrigger.gameObject.SetActive(isActive);
            _bodyEventTrigger.gameObject.SetActive(!isActive);
        }

        /// <summary>
        /// Takes the raw joystick input and converts into camera rotation space.
        /// For example, pushing forward on a joystick will result in a vector that points where the camera is looking.
        /// </summary>
        private Vector3 GetCameraSpaceMovementInput()
        {
            _rawInput = Vector2.zero;
            _rawInput.x = _controls.GetAxis(Controls.MovementAxis.MoveHorizontal);
            _rawInput.z = _controls.GetAxis(Controls.MovementAxis.MoveVertical);
            float inputMagnitude = _rawInput.magnitude;
            if (inputMagnitude > 1f)
            {
                _rawInput.Normalize();
            }

            if (inputMagnitude > Mathf.Epsilon && !_hasPlayerInputted)
            {
                _hasPlayerInputted = true;
                Events.RaiseEvent(GTWEvents.PLAYER_FIRST_INPUT);// we are not sure what this does.
                // It's possible dom copied this from atrio? We're too scared to remove it.
            }

            // Vector3 cameraForward = PlayerTransform.position - PlayerCameraTransform.position;
            Vector3 cameraForward = PlayerVirtualCameraController.AimTarget.forward;
            cameraForward.y = 0f;
            cameraForward.Normalize();
            Quaternion cameraRotation = Quaternion.LookRotation(cameraForward, Vector3.up);

            return cameraRotation * _rawInput;
        }

        /// <summary>
        /// Takes the movement input and converts it to local space of the player.
        /// This helps simplify certain calculations that need to know what the input vector is
        /// in relation to the characters local rotation space.
        ///
        /// Used when you wiggle when grabbing (specifically on flat ground), aka spider mode.
        /// It's very helpful to know what the joystick input is in terms of the characters left and right.
        /// </summary>
        private Vector3 GetCharacterSpaceMovementInput()
        {
            Vector3 playerForward = Vector3.up;
            Vector3 playerUp = PlayerModelController.transform.up;

            // 10f is the arbitrary angle we determined to be considered "flat ground".
            if (Vector3.Angle(Vector3.up, PlayerModelController.transform.up) < 10f)
            {
                playerForward = Vector3.ProjectOnPlane(PlayerVirtualCameraController.AimTarget.forward, Vector3.up);
            }

            // If the player is almost on a vertical wall/on a vertical wall/or upside down
            // and you press forward, they'll actually go backward.
            // That feels wrong from the camera's perspective- so if we detect that, we flip the input.

            // Detects if 1) the player is on a vertical wall or upside down and
            //            2) if the camera is looking in the same direction as the up vector.
            // If that's the case, we need to flip the side vector.

            // If the playerUp.y is under 0.5f, we've arbitrarily decided it means the player is on flat ground or upside down.
            // 90f is an arbitrary general direction to see if the camera is generally looking towards the player's up vector.
            // If its lower than 90 degrees it means the camera is looking from underneath the player (i.e towards the players belly)
            float side = 1f;
            if (playerUp.y < 0.5f && Vector3.Angle(playerUp, PlayerVirtualCameraController.AimTarget.forward) < 90f)
            {
                side = -1f;
            }

            Quaternion inputRotation = Quaternion.LookRotation(playerUp, playerForward);

            Vector3 input = inputRotation * Vector3.up * _rawInput.z + inputRotation * Vector3.left * side * _rawInput.x;
            DebugExtensions.DrawArrow(PlayerPosition, 0.1f, input, Color.magenta);
            return input;
        }

        /// <summary>
        /// We need precise directions and speeds in Update() Time (as opposed to physicsUpdate, since they run at different speeds) to do
        /// calculations.
        ///
        /// Knowing these in Update() prevents stuttering.
        /// </summary>
        public void UpdateDirectionalVectorsAndSpeed()
        {
            if (Time.timeScale == 0f || Mathf.Approximately(Time.deltaTime, 0f))
            {
                return;
            }

            _velocity = (PlayerTransform.position - _lastPosition);
            _speed = _velocity.magnitude;

            if (_speed > 0.001f)
            {
                _timeSinceLastMoving = 0f;

                Vector3 normalizedVelocity = _velocity.normalized;
                Vector3 upVector = Vector3.up;

                float dotUp = Vector3.Dot(normalizedVelocity, Vector3.up);
                if (Mathf.Abs(dotUp) < 0.999f)
                {
                    //Set new direction only if it is not going straight upward or downward
                    _direction = normalizedVelocity;
                    upVector = _directionUp;

                    _directionSmooth = Vector3.Lerp(_oldDirectionSmooth, normalizedVelocity, _speed).normalized;
                    _oldDirectionSmooth = _directionSmooth;
                }

                Vector3 directionSide = Vector3.Cross(_direction, upVector);
                _directionUp = Vector3.Cross(directionSide, _direction);

                Vector3 directionSideSmooth = Vector3.Cross(_directionSmooth, upVector);
                _directionUpSmooth = Vector3.Cross(directionSideSmooth, _directionSmooth);

                Vector3 directionFlat = _velocity;
                directionFlat.y = 0f;
                if (directionFlat.sqrMagnitude > 0.00000001f)
                {
                    _directionFlat = directionFlat.normalized;
                }

                Vector3 directionFlatSmooth = _directionSmooth;
                directionFlatSmooth.y = 0f;
                if (directionFlatSmooth.sqrMagnitude > 0.00000001f)
                {
                    _directionFlatSmooth = directionFlatSmooth.normalized;
                }
            }
            else
            {
                _speed = 0f;
                _velocity = Vector3.zero;

                _timeSinceLastMoving += Time.deltaTime;
            }

            float speedRaw = Rigidbody.linearVelocity.magnitude;

            _velocityAccelerationRaw = (Rigidbody.linearVelocity - _lastVelocityRaw);
            _velocityAccelerationSpeedRaw = speedRaw - _lastSpeedRaw;

            _speed /= Time.deltaTime;
            _speedKPH = _speed * 3.6f;
            _velocity /= Time.deltaTime;
            _velocityAccelerationRaw /= Time.deltaTime;
            _velocityAccelerationSpeedRaw /= Time.deltaTime;

            _velocityAcceleration = (_velocity - _lastVelocity);
            _velocityAccelerationSpeed = (_speed - _lastSpeed);

            _lastSpeed = _speed;
            _lastVelocity = _velocity;
            _lastSpeedRaw = speedRaw;
            _lastVelocityRaw = Rigidbody.linearVelocity;

            _velocityAcceleration /= Time.deltaTime;
            _velocityAccelerationSpeed /= Time.deltaTime;

            _lastPosition = PlayerTransform.position;
        }

        /// <summary>
        /// Velocity at the point of the pelvis.
        /// It's only different than the ball's velocity when in ragdoll mode.
        /// </summary>
        public void UpdateBodyVelocity()
        {
            _bodyVelocity = Rigidbody.linearVelocity;

            if (IsRagdollState)
            {
                _bodyVelocity = PlayerModelController.PlayerIkController.PlayerRagdoll.PelvisRigidBody.linearVelocity;
            }

            _bodySpeed = _bodyVelocity.magnitude;
            _bodySpeedKPH = _bodySpeed * 3.6f;
        }

        public void UpdateDirectionalVectorsInNoClipMode()
        {
            Vector3 camVector = PlayerPosition - PlayerCameraTransform.position;
            camVector.y = 0f;
            camVector.Normalize();
            _direction = camVector;
            _directionUp = Vector3.up;

            _velocity = (PlayerTransform.position - _lastPosition);
            _speed = _velocity.magnitude;

            _speed /= Time.deltaTime;
            _speedKPH = _speed * 3.6f;
            _velocity /= Time.deltaTime;

            _lastPosition = PlayerTransform.position;
            _lastRotation = PlayerTransform.rotation;
        }

        /// <summary>
        ///  We need precise angular velocity in Update() Time (as opposed to physicsUpdate, since they run at different speeds) to do
        /// calculations.
        ///
        /// Knowing these in Update() prevents stuttering.
        /// </summary>
        public void UpdateAngularVelocity()
        {
            if (_speed > 0.0001f)
            {
                Quaternion angularVelocity = PlayerTransform.rotation * Quaternion.Inverse(_lastRotation);

                Vector3 angularVelocityVector = new Vector3(angularVelocity.x, angularVelocity.y, angularVelocity.z);

                _angularVelocityUp = _playerCollisionHandler.IsGrounded
                    ? -_playerCollisionHandler.FloorDirection
                    : Vector3.up;
                Vector3 angularVelocityDirection = Vector3.Cross(angularVelocityVector, _angularVelocityUp);
                _angularVelocityDirection = angularVelocityDirection.normalized;

                _angularVelocityDirectionSmooth = Vector3.Lerp(_oldAngularVelocityDirectionSmooth, angularVelocityDirection.normalized, _speed).normalized;
                _oldAngularVelocityDirectionSmooth = _angularVelocityDirectionSmooth;

                _lastRotation = PlayerTransform.rotation;
            }


            //Calculate Surface velocity
            float angularSpeed = Rigidbody.angularVelocity.magnitude;

            _ballSurfaceSpeed = angularSpeed * BallRadius;
            _ballSurfaceVelocity = _angularVelocityDirection * _ballSurfaceSpeed;

        }

        public void UpdateNoClipMovements()
        {
            Vector3 input = _movementInput;
            float verticalInput =
                (InputControls.GetButton(GTWUserActions.NOCLIPMOVEUP) ? 1f : 0f) +
                (InputControls.GetButton(GTWUserActions.NOCLIPMOVEDOWN) ? -1f : 0f);

            _noClipVerticalInput = Mathf.MoveTowards(_noClipVerticalInput, verticalInput, Time.deltaTime * 5f);
            input.y = _noClipVerticalInput;

            _noClipVelocity = Vector3.MoveTowards(_noClipVelocity, input, Time.deltaTime * 5f);

            float speed = 10f;
            bool isHighSpeed = noClipHighSpeedMode;

            if (InputControls.GetButton(GTWUserActions.NOCLIPHIGHSPEED))
            {
                isHighSpeed = !isHighSpeed;
            }

            if (isHighSpeed)
            {
                speed *= noClipHighSpeedMultiplier;
            }

            PlayerTransform.position += _noClipVelocity * Time.deltaTime * speed;
        }


        /// <summary>
        /// Handles when the player hits the "grab" button.
        ///
        /// Determines whether the player can grab, and if they can, initiates the grab.
        /// Records the current target position for the grab for the UpdateGrab.
        /// </summary>
        public void ActivateGrabLink()
        {
            if (_isGrabLinkActive)
            {
                return;
            }

            if (_grabCooldownTimeLeft > 0f)
            {
                return;
            }

            PlayerCollisionHandler.GroundHitData groundHitData = PlayerCollisionHandler.CurrentCurrentGroundHitData;

            if (!PlayerCollisionHandler.IsGrounded)
            {
                if (PlayerCollisionHandler.PreviousGroundHitData.time < _grabGracePeriod)
                {
                    groundHitData = PlayerCollisionHandler.PreviousGroundHitData;
                    PlayerCollisionHandler.Reground();
                }

                if (!PlayerCollisionHandler.IsGrounded)
                {
                    return;
                }
            }

            RetargetGrabLink();

            if (_grabRagdoll)
            {
                PlayerModelController.PlayerGrounder.UpdateGrounder();

                if (!(PlayerModelController.PlayerGrounder.LeftHandGrounded ||
                      PlayerModelController.PlayerGrounder.RightHandGrounded))
                {
                    return;
                }

                PlayerIkController.PlayerRagdoll.SetRagdollActive(true);
                PlayerIkController.PlayerRagdoll.ActivateHandFixedJoints(groundHitData.hit.transform);
            }
            else
            {
                Vector3 targetPosition = GrabTargetPosition;
                Quaternion targetRotation = GrabTargetRotation;
                bool isStickySurface = _grabLinkCollider.gameObject.tag == PlayerCollisionHandler.StickyTag;

                Rigidbody.MoveRotation(targetRotation);

                if (isStickySurface)
                {
                    Rigidbody.linearVelocity = (targetPosition - Rigidbody.position) / FixedTimeSync.FixedDeltaTime;
                }
                else
                {
                    Rigidbody.linearVelocity = Vector3.ProjectOnPlane(Rigidbody.linearVelocity, groundHitData.FloorNormal);
                }

                Rigidbody.angularVelocity = Vector3.zero;
                _lastGrabTargetPosition = targetPosition;
                _lastGrabVelocity = Velocity;
                _lastGrabVelocities.Clear();
                AddLastGrabVelocity(_lastGrabVelocity);
                _lastGrabRigidbodyVelocity = Rigidbody.linearVelocity;
            }

            _grabHoldVelocityTime = -1f;

            _grabCooldownTimeLeft = GRAB_COOLDOWN;
            _isGrabLinkActive = true;
            OnActivateGrab?.Invoke();
        }

        private void RetargetGrabLink()
        {
            if (!PlayerCollisionHandler.IsGrounded)
            {
                return;
            }

            RaycastHit hit = PlayerCollisionHandler.Hit;
            _grabLinkParent = hit.transform;
            _grabLinkCollider = hit.collider;

            _grabLinkNormal = hit.normal;

            Vector3 side = Vector3.Cross(hit.normal, PlayerModelController.transform.forward);
            Vector3 forward = Vector3.Cross(side, hit.normal);
            Quaternion rotation = Quaternion.LookRotation(forward, hit.normal);

            _grabLinkParentRigidbody = _grabLinkParent.GetComponent<Rigidbody>();
            Vector3 parentVelocity = _grabLinkParentRigidbody != null ? _grabLinkParentRigidbody.linearVelocity : Vector3.zero;

            Vector3 position = hit.point + hit.normal * (BallRadius) + (parentVelocity * FixedTimeSync.FixedDeltaTime);

            _grabLocalLocation = new GTWExtensions.Location()
            {
                Position = _grabLinkParent.InverseTransformPoint(position),
                Rotation = Quaternion.Inverse(_grabLinkParent.rotation) * rotation
            };
        }

        public void DeactivateGrabLink(float grabCooldown)
        {
            _grabCooldownTimeLeft = grabCooldown;

            if (!_isGrabLinkActive)
                return;
            if (_forceHoldGrab)
                return;

            DeactivateGrabLink();
        }

        public void DeactivateGrabLink()
        {
            if (!_isGrabLinkActive)
                return;
            if (_forceHoldGrab)
                return;

            if (_grabbingStickySurface)
            {
                //Conserve velocity from moving sticky surface
                SetVelocity(_lastGrabVelocity, Vector3.zero);
            }
            // DebugExtensions.DrawArrow(PlayerPosition, PlayerPosition + _lastGrabVelocity, Color.red, 0.1f, 5f);

            if (_grabRagdoll)
            {
                Vector3 toPelvis = PlayerIkController.PelvisBone.position - GrabTargetPosition;
                float verticalForce = Mathf.Clamp01(Vector3.Angle(Vector3.down, toPelvis) / 90f);

                Rigidbody.linearVelocity = new Vector3(0f, verticalForce * _ledgeGrabFlingVerticalForce, 0f);
            }


            if (!_grabLinkCollider.gameObject.CompareTag(PlayerCollisionHandler.SlipperyTag) && !_grabLinkCollider.gameObject.CompareTag(PlayerCollisionHandler.StickyTag))
            {
                _highFrictionModeTime = 0.25f;
            }

            PlayerIkController.PlayerRagdoll.SetRagdollActive(false);
            PlayerIkController.PlayerRagdoll.DeactivateHandFixedJoints();

            _isGrabLinkActive = false;
            _grabbingStickySurface = false;

            _grabRagdoll = false;

            OnDeactivateGrab?.Invoke();
        }

        public void TurnPlayerTowardCamera()
        {
            Vector3 camVector = PlayerPosition - PlayerCameraTransform.position;
            camVector.y = 0f;
            camVector.Normalize();
            _directionFlat = camVector;
            _directionFlatSmooth = _directionFlat;

            _angularVelocityDirection = _directionFlat;
            _angularVelocityDirectionSmooth = _directionFlat;
            _oldAngularVelocityDirectionSmooth = _directionFlat;

            Vector3 side = Vector3.Cross(_directionFlat, _directionUp);
            _direction = Vector3.Cross(_directionUp, side);
            _directionSmooth = _direction;
            _oldDirectionSmooth = _direction;
            // DebugExtensions.DrawArrow(PlayerPosition, PlayerPosition + _direction, Color.white, 0.1f, 5f);
        }

        /// <summary>
        /// A mega function to handle most of the grab functionality
        /// 1. Checks every frame if the grab should be broken or should continue to grab.
        /// 2. We handle several use cases: Grabbing on a ledge (which enters ragdoll mode) and grabbing on flat ground/walls.
        ///
        /// For flat ground/walls:
        /// a) We allow a little bit of sliding because it feels good (i.e. when you hit the grab button, having it immediately stop
        /// doesn't feel very satisfying.) The sliding also adds a bit more challenge to the game/ makes the grab a little less OP.
        ///
        /// b) If the grab is still active, adds a little bit of velocity to ensure the balls position doesn't move too far away from
        /// the target grab position.
        /// </summary>
        private void UpdateGrabLinkFixedUpdate()
        {
            if (_isGrabLinkActive)
            {
                if (_grabLinkParent == null || !_grabLinkParent.gameObject.activeInHierarchy ||
                    _grabLinkCollider == null || !_grabLinkCollider.enabled)
                {
                    DeactivateGrabLink();
                    return;
                }


                if (_grabRagdoll)
                {
                    //Ledge grab

                    Rigidbody.linearVelocity = Vector3.zero;
                    Rigidbody.angularVelocity = Vector3.zero;
                    Rigidbody.MovePosition(PlayerIkController.PlayerRagdoll.RagdollBallTarget.position);

                    PlayerIkController.PlayerRagdoll.UpdateHandFixedJoints();

                    Vector3 buttVelocity = MovementInput * _ledgeGrabForceControl;
                    PlayerIkController.PlayerRagdoll.PelvisRigidBody.AddForce(buttVelocity, ForceMode.Acceleration);
                }
                else
                {
                    // Grabbing on Flat ground/walls

                    _grabbingStickySurface = _grabLinkCollider.gameObject.CompareTag(PlayerCollisionHandler.StickyTag);
                    bool isSlipperySurface = _grabLinkCollider.gameObject.layer == _slipperyLayer;

                    float distance = Vector3.Distance(Rigidbody.transform.position, _lastGrabTargetPosition);
                    // Debug.Log("Distance: " + distance + "  lastMag: " + _lastGrabRigidbodyVelocity.magnitude);

                    if (!PlayerCollisionHandler.IsGrounded || distance > (_grabBreakForce + _lastGrabRigidbodyVelocity.magnitude))
                    {
                        Debug.Log("break grab:" + distance);
                        DeactivateGrabLink();
                        return;
                    }

                    if (!_grabbingStickySurface)
                    {
                        RetargetGrabLink();

                        // Determine if we transition from flat ground grab into Ledge Grab
                        if (PlayerCollisionHandler.FloorAngle > 110f)
                        {
                            PlayerModelController.UpdateModelPositionWhileGrabbing(true);
                            PlayerModelController.PlayerGrounder.UpdateGrounder();
                            PlayerIkController.PlayerRagdoll.SetRagdollActive(true);
                            PlayerIkController.PlayerRagdoll.ActivateHandFixedJoints(PlayerCollisionHandler.Hit.transform);
                            _grabRagdoll = true;
                            return;
                        }
                    }

                    Vector3 targetPosition = GrabTargetPosition;
                    Quaternion targetRotation = GrabTargetRotation;

                    // DebugExtensions.DrawLocator(targetPosition, Color.red, 0.3f, 5f);
                    if (!_grabbingStickySurface)
                    {
                        if (PlayerCollisionHandler.FloorAngle > 45f)
                        {
                            // Sliding while grabbing on normal surface

                            float maxGravity = isSlipperySurface ? 0.1f : 0.05f;

                            if (Rigidbody.linearVelocity.y > Gravity.y * maxGravity)
                            {
                                Rigidbody.AddForce(Gravity, ForceMode.Acceleration);
                            }
                        }

                        Rigidbody.AddForce((targetPosition - Rigidbody.position).normalized, ForceMode.Acceleration);
                    }
                    else
                    {
                        // Remove any sliding. Perfect grab on sticky surface
                        Rigidbody.linearVelocity = (targetPosition - Rigidbody.transform.position) / FixedTimeSync.FixedDeltaTime;
                    }

                    // Reset the angular velocity because the ball should be frozen in place while grabbing
                    // If you don't, when you release the grab you get some residual angular velocity (which you don't want)
                    Rigidbody.angularVelocity = Vector3.zero;

                    // Changes based on the position/rotation of what you're currently grabbing
                    // i.e. grabbing on a sticky surface of the giant wheel, it will follow its position
                    Rigidbody.MoveRotation(targetRotation);

                    // Storing values so we can compare next frame
                    _lastGrabTargetPosition = targetPosition;
                    _lastGrabRigidbodyVelocity = Rigidbody.linearVelocity;


                    AddLastGrabVelocity(Rigidbody.linearVelocity);
                    _lastGrabVelocity = LastGrabVelocityAverage();
                    _lastGrabVelocity = Rigidbody.linearVelocity.normalized * _lastGrabVelocity.magnitude;
                }
            }
        }

        /// <summary>
        /// Adds a value to a list containing the velocity over the last 5 frames.
        /// Used to calculate an average velocity.
        ///
        /// We update the FixedTime every 30 frames to match the rendering frame rate so that the physics framerate is equal.
        /// We do this because it reduces some stutters in game.
        ///
        /// However, when you do this matching, it causes invisible stutters in the physics values.
        /// I.e. grabbing a velocity when you change the fixed time, it could be very wrong.
        /// By using the average, we mitigate these errors.
        /// </summary>
        private void AddLastGrabVelocity(Vector3 velocity)
        {
            _lastGrabVelocities.Enqueue(velocity);

            while (_lastGrabVelocities.Count > 5)
            {
                _lastGrabVelocities.Dequeue();
            }
        }

        /// <summary>
        /// Used to mitigate calculation errors because the FixedTime and Rendering framerates are different.
        /// </summary>
        /// <returns>The average of the last 5 grab velocities.</returns>
        private Vector3 LastGrabVelocityAverage()
        {
            Vector3 velocityAverage = Vector3.zero;

            if (_lastGrabVelocities.Count == 0)
            {
                return velocityAverage;
            }

            foreach (Vector3 velocity in _lastGrabVelocities)
            {
                velocityAverage += velocity;
            }

            return velocityAverage / _lastGrabVelocities.Count;
        }

        private float _grabHoldVelocityTime = 0f;

        /// <summary>
        /// Handles anything that can't be handled in the FixedUpdate (i.e. UpdateGrabLinkFixedUpdate()).
        /// i.e. Handles anything that needs to happen at the Update() framerate.
        /// </summary>
        // Update the body position while you are swinging from a ledge (moves the pelvis IK effectors)
        private void UpdateGrabLink()
        {
            // Update the body position while you are swinging from a ledge (moves the pelvis IK target)
            if (_isGrabLinkActive && !_grabRagdoll)
            {
                Vector3 input = CharacterSpaceInput;
                // Converting the input to a force to move the character.
                // We are multiplying by these magic values based on the character size (if its too high, the player will move too fast or his arms won't stretch too much).
                // Values were picked based on trial and error.
                // By using VelocityAcceleration, if you're going really fast and then grabbing on the floor, the character's IK will move (i.e. his animation will react/ show momentum).
                Vector3 force = Vector3.ClampMagnitude(-VelocityAcceleration * 0.01f, 0.1f) +
                                input * 0.1f;

                PlayerIkController.SetBodyHorizontalForce(force);
            }
        }

        protected override void OnPlayerTakeDamage(object sender, HealthEventArgs e)
        {
            throw new NotImplementedException();
        }

        protected override void OnPlayerKilled(object sender, HealthEventArgs e)
        {
            throw new NotImplementedException();
        }

        public override void SetMovementEnabled(bool active)
        {
            throw new NotImplementedException();
        }

        [ContextMenu("PrintCharacterInfo")]
        private void PrintCharacterInfo()
        {
            StringBuilder sb = new StringBuilder("Player State Info: ...\n");
            sb.Append($"Player Transform P={this.transform.position}\n");
            //sb.Append($"Player FollowCam Transform P={_playerCameraTransform.position}\n");
            sb.Append(".\n");
            sb.Append($"Player Rigidbody Transform P={_rigidbody.transform.position} (correct PlayerPosition)\n");
            sb.Append($"Player Ragdoll Pelvis Transform P={_bodyEventTrigger.transform.position}\n");
            var physicsOverride = PlayerPhysicsOverride?.GetOverride();
            sb.Append($"Player Physics Override {NullMsg(physicsOverride)} source={GOName(physicsOverride?.source)}\n");
            sb.Append(".\n");
            CinemachineVirtualCamera test = PlayerVirtualCameraController?.CVC;
            sb.Append($"CinemachineVirtualCamera standby mode={NullVal(test?.m_StandbyUpdate.ToString())}\n");
            sb.Append($"Player AimTarget Transform P={NullVal(PlayerVirtualCameraController?.AimTarget.ToString())}\n");
            sb.Append($"Player FollowCam Transform P={NullVal(PlayerVirtualCameraController?.FollowCam.ToString())}\n");
            sb.Append($"Player FollowPosition Transform P={NullVal(PlayerVirtualCameraController?.FollowPosition.ToString())}\n");
            var cameraOverride = PlayerVirtualCameraController?.PlayerCameraOverride?.GetOverride();
            sb.Append($"Player Camera Override {NullMsg(cameraOverride)} source={GOName(cameraOverride?.source)}\n");
            sb.Append(".\n");
            PlayerRagdoll pdoll = PlayerModelController?.PlayerIkController?.PlayerRagdoll;
            bool pragenabled = pdoll ? pdoll.IsRagdollEnabled : false;
            RagdollUtility dutil = pdoll?.RagdollUtility;
            bool utilityenabled = dutil ? dutil.isRagdoll : false;
            sb.Append($"Player Ragdoll IsRagdollState={IsRagdollState}, IsRagEnabled={pragenabled}, IsRagdoll={utilityenabled}\n");
            sb.Append(".\n");
            sb.Append($"Player grabAbilityAvailable={grabAbilityAvailable}\n");
            sb.Append($"Player slowMotionAbilityAvailable={slowMotionAbilityAvailable}\n");
            sb.Append($"Player noClipAbilityAvailable={noClipAbilityAvailable}\n");
            sb.Append($"Player ragDollAbilityAvailable={ragDollAbilityAvailable}\n");
            sb.Append($"Player restartAbilityAvailable={restartAbilityAvailable}\n");
            sb.Append($"Player restartAbilityAvailable={restartStartingCheckpointAbilityAvailable}\n");
            sb.Append($"Player noClipHighSpeedMode={noClipHighSpeedMode}\n");
            sb.Append(".\n");
            sb.Append($"Player IsMoveState={IsMoveState}, Velocity={Velocity}, BallSurfaceVelocity={BallSurfaceVelocity}, Speed={Speed}\n");
            sb.Append($"Player BodyVelocity={BodyVelocity}, BodySpeed={BodySpeed}, BodySpeedKPH={BodySpeedKPH}\n");
            sb.Append($"Player Direction={Direction}, GravityDirection={GravityDirection}, Gravity={Gravity}\n");
            sb.Append($"Player AngularVelocityDirection={AngularVelocityDirection}\n");
            sb.Append($"Player IsMoving={IsMoving}, IsBodyMoving={IsBodyMoving}\n");
            sb.Append(".\n");
            sb.Append($"Player IsGrabLinkActive={IsGrabLinkActive}\n");
            sb.Append($"Player IsGrabRagDoll={IsGrabRagDoll}\n");
            sb.Append($"Player GrabbingStickySurface={GrabbingStickySurface}\n");
            sb.Append($"Player GrabLinkCollider={GOName(_grabLinkCollider)}\n");
            sb.Append($"Player GrabLinkNormal={GrabLinkNormal}\n");
            sb.Append($"Player GrabLinkParentRigidbody={GOName(_grabLinkParentRigidbody)}\n");
            sb.Append($"Player IsGrabLinkActive={IsGrabLinkActive}\n");
            if (GrabLinkParent != null)
            {
                sb.Append($"Player GrabTargetPosition={GrabTargetPosition}\n");
                sb.Append($"Player GrabTargetRotation={GrabTargetRotation}\n");
            }
            sb.Append($"Player HighFrictionMode={HighFrictionMode}\n");
            sb.Append(".\n");
            sb.Append($"Player ForceHoldGrab={ForceHoldGrab}\n");
            sb.Append($"Player _grabCooldownTimeLeft={_grabCooldownTimeLeft}\n");
            sb.Append($"Player _grabLocalLocation position={_grabLocalLocation.Position}, rotation={_grabLocalLocation.Rotation}\n");
            sb.Append($"Player _lastGrabTargetPosition={_lastGrabTargetPosition}\n");
            sb.Append($"Player _lastGrabVelocity={_lastGrabVelocity}\n");
            sb.Append($"Player _lastGrabRigidbodyVelocity={_lastGrabRigidbodyVelocity}\n");
            sb.Append($"Player _highFrictionModeTime={_highFrictionModeTime}\n");
            sb.Append($"Player _slipperyLayer={_slipperyLayer}\n");
            sb.Append($"Player _hasPlayerInputted={_hasPlayerInputted}\n");

            Debug.LogError(sb.ToString());
        }

        private string NullVal(string value)
        {
            if (value == null)
            {
                return "(...)";
            }
            else
            {
                return value;
            }
        }

        private string GOName(object obj)
        {
            if (obj == null)
            {
                return "(...)";
            }

            var comp = obj as Component;
            if (comp != null)
            {
                obj = comp.gameObject;
            }

            var go = obj as GameObject;
            if (go == null)
            {
                return "(...)";
            }
            else
            {
                return go.name;
            }
        }

        private string NullMsg(object obj)
        {
            if (obj == null)
            {
                return "(is null)";
            }
            else
            {
                return "(is not null)";
            }
        }
    }
}