// Copyright Isto Inc.

using Isto.Core.Game;
using Isto.Core.Installers;
using Isto.Core.UI;
using Isto.GTW.Data;
using Isto.GTW.Managers;
using UnityEngine;

namespace Isto.GTW
{
    public class TitleMenuSceneInstaller : SimpleTitleMenuSceneInstaller
    {
        [SerializeField] private TitleScreenPlayerDataManager _titleScreenPlayerDataManager;


        public override void InstallBindings()
        {
            Container.Bind<TitleMenuStateMachine>().FromComponentInHierarchy().AsSingle();
            Container.Bind<SimpleTitleMenuStateMachine>().To<TitleMenuStateMachine>().FromResolve();
            Container.Bind<IUIMenu>().FromComponentInHierarchy().AsSingle();

            // These bindings are used to load the level times and progress
            // Since these are done on the scene installer, they will be destroyed when we transition to a new scene
            Container.Bind<IGameProgressProvider>().To<GTWProgressProvider>().FromNewComponentOnNewGameObject().AsSingle();
            Container.Bind<LevelVariables>().FromNewComponentOnNewGameObject().AsSingle();
            // Container.Bind<DoinklerLevelVariables>().FromNewComponentOnNewGameObject().AsSingle();
            // Container.Bind<DoinklerLevelDataManager>().FromNewComponentOnNewGameObject().AsSingle().NonLazy();
            Container.Bind<LevelDataManager>().FromNewComponentOnNewGameObject().AsSingle().NonLazy();
            Container.Bind<TitleScreenPlayerDataManager>().FromComponentInNewPrefab(_titleScreenPlayerDataManager).AsSingle().NonLazy();
        }
    }
}