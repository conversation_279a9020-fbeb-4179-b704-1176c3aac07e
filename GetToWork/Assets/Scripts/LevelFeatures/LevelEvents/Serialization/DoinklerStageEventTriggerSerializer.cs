// Copyright Isto Inc.

using Isto.Core.Game;
using Isto.GTW.Data;
using UnityEngine;
using Zenject;

namespace Isto.GTW.LevelFeatures.LevelEvents
{
    [RequireComponent(typeof(LevelTransitionPopupEvent))]
    public class DoinklerStageEventTriggerSerializer : LevelEvent
    {
        // OTHER FIELDS

        private LevelTransitionPopupEvent _levelTransitionEvent;
        private int _lastTriggeredFrame = -1;


        // UNITY HOOKUPS

        [Space]
        [Tooltip("Add value field to the current variable value.")]
        [SerializeField] private bool _additive;

        [Tooltip("Prevent from setting variable to a new value lower value.")]
        [SerializeField] private bool _blockDecrement;

        [Tooltip("Prevent from setting variable to a new value higher value.")]
        [SerializeField] private bool _blockIncrement;

        // INJECTION

        private IGameProgressProvider _gameProgressProvider;
        private DoinklerLevelDataManager _doinklerLevelDataManager;

        [Inject]
        public void Inject(IGameProgressProvider gameProgressProvider, DoinklerLevelDataManager doinklerLevelDataManager)
        {
            _gameProgressProvider = gameProgressProvider;
            _doinklerLevelDataManager = doinklerLevelDataManager;
        }

        protected override void Awake()
        {
            base.Awake();
            _levelTransitionEvent = GetComponent<LevelTransitionPopupEvent>();
        }

        public override void TriggerEvent(bool initialization = false)
        {
            // Put up this guard against multiple calls in the same frame as this was causing the
            // timers to increase by double. Was unable to track down the source of the duplicate calls.
            if (_lastTriggeredFrame == Time.frameCount)
            {
                return;
            }
            _lastTriggeredFrame = Time.frameCount;

            int currentLevel = _gameProgressProvider.GetCurrentGameProgressLevel();
            float currentLevelTime = _gameProgressProvider.GetGameSecondsElapsedInLevel(currentLevel);

            float retrievedBestValue = PlayerPrefs.GetFloat(_levelTransitionEvent.CurrentLevel.UniqueIDBest, 0f);
            float retrievedTotalValue = PlayerPrefs.GetFloat(_levelTransitionEvent.CurrentLevel.UniqueIDTotal, 0f);

            PlayerPrefs.SetFloat(_levelTransitionEvent.CurrentLevel.UniqueIDTotal, retrievedTotalValue + currentLevelTime);

            if (retrievedBestValue == 0f || currentLevelTime < retrievedBestValue)
            {
                PlayerPrefs.SetFloat(_levelTransitionEvent.CurrentLevel.UniqueIDBest, currentLevelTime);
            }
        }
    }
}